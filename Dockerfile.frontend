# AQUESA Frontend Dockerfile
FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies for frontend server
RUN pip install --no-cache-dir python-dotenv

# Copy frontend files
COPY frontend/ .
COPY .env* ./

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash aquesa && \
    chown -R aquesa:aquesa /app
USER aquesa

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/ || exit 1

# Default command
CMD ["python", "server.py", "--port", "3000"]
