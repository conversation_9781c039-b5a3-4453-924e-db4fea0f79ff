# AQUESA Leak Detection System Environment Configuration Template
# Copy this file to .env and update the values as needed

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB Connection URI - Replace with your actual MongoDB connection string
MONGO_URI=mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority

# Database Name
DB_NAME=aquesa_management

# =============================================================================
# API SERVER CONFIGURATION
# =============================================================================

# API Server Host and Port
API_HOST=0.0.0.0
API_PORT=8000

# Enable/Disable API auto-reload for development
API_RELOAD=true

# =============================================================================
# FRONTEND SERVER CONFIGURATION
# =============================================================================

# Frontend Server Port
FRONTEND_PORT=3000

# API Base URL for frontend to connect to backend
API_BASE_URL=http://localhost:8000/api

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Time window for historical data analysis (days)
WINDOW_SIZE=10

# Threshold multipliers for leak detection
HOURLY_THRESHOLD=1.2
DAILY_THRESHOLD=1.2

# Monitoring intervals (seconds)
MONITOR_INTERVAL=10
RETRAIN_INTERVAL=3600

# =============================================================================
# LEAK DETECTION PARAMETERS
# =============================================================================

# Forgotten tap detection settings
FORGOTTEN_TAP_THRESHOLD=0.5
FORGOTTEN_TAP_DURATION=30

# Tap leakage detection threshold
TAP_LEAKAGE_THRESHOLD=0.05

# Model file path
MODEL_FILE=isolation_forest_model.pkl

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Enable/Disable detailed logging
VERBOSE_LOGGING=false

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# CORS settings for API
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Enable/Disable CORS for all origins (use only in development)
CORS_ALLOW_ALL=true

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Environment mode (development, production, testing)
ENVIRONMENT=development

# Enable/Disable debug mode
DEBUG=true

# Use test/mock data instead of real database
USE_MOCK_DATA=false

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================

# Enable/Disable email notifications for leaks
EMAIL_NOTIFICATIONS=false

# Email configuration (if notifications enabled)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Recipients for leak notifications
NOTIFICATION_EMAILS=<EMAIL>,<EMAIL>

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Maximum number of records to process in one batch
BATCH_SIZE=1000

# Connection pool settings
DB_MIN_POOL_SIZE=1
DB_MAX_POOL_SIZE=10

# Cache settings
ENABLE_CACHING=true
CACHE_TTL=300

# =============================================================================
# BACKUP AND MAINTENANCE
# =============================================================================

# Enable automatic backups
AUTO_BACKUP=false

# Backup directory
BACKUP_DIR=./backups

# Data retention period (days)
DATA_RETENTION_DAYS=365
