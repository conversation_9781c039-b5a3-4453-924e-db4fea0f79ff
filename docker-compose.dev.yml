# Docker Compose for Development Environment
version: '3.8'

services:
  # AQUESA API Backend Service (Development)
  aquesa-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: aquesa-api-dev
    ports:
      - "${API_PORT:-8000}:8000"
    environment:
      - MONGO_URI=${MONGO_URI}
      - DB_NAME=${DB_NAME:-aquesa_management}
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - ENVIRONMENT=development
      - DEBUG=true
      - API_RELOAD=true
      - LOG_LEVEL=DEBUG
      - VERBOSE_LOGGING=true
      - CORS_ALLOW_ALL=true
      - USE_MOCK_DATA=${USE_MOCK_DATA:-false}
    volumes:
      - .:/app
      - ./models:/app/models
      - ./logs:/app/logs
      - ./backups:/app/backups
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    restart: unless-stopped
    networks:
      - aquesa-network

  # AQUESA Frontend Service (Development)
  aquesa-frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: aquesa-frontend-dev
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      - FRONTEND_PORT=3000
      - API_BASE_URL=http://localhost:${API_PORT:-8000}/api
    volumes:
      - ./frontend:/app
    depends_on:
      - aquesa-api
    restart: unless-stopped
    networks:
      - aquesa-network

  # Test API Server (for development/testing)
  aquesa-test-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: aquesa-test-api
    ports:
      - "8001:8000"
    environment:
      - ENVIRONMENT=testing
      - DEBUG=true
      - USE_MOCK_DATA=true
    volumes:
      - .:/app
    command: ["python", "test_api_server.py"]
    restart: unless-stopped
    networks:
      - aquesa-network
    profiles:
      - testing

networks:
  aquesa-network:
    driver: bridge
