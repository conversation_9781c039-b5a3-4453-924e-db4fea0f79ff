import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings:
    # Database Configuration
    MONGO_URI = os.getenv(
        "MONGO_URI",
        "mongodb+srv://vijaybhaskar:<EMAIL>/?retryWrites=true&w=majority"
    )
    DB_NAME = os.getenv("DB_NAME", "aquesa_management")

    # API Server Configuration
    API_HOST = os.getenv("API_HOST", "0.0.0.0")
    API_PORT = int(os.getenv("API_PORT", "8000"))
    API_RELOAD = os.getenv("API_RELOAD", "true").lower() == "true"

    # Frontend Configuration
    FRONTEND_PORT = int(os.getenv("FRONTEND_PORT", "3000"))
    API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000/api")

    # Monitoring Configuration
    WINDOW_SIZE = int(os.getenv("WINDOW_SIZE", "10"))  # days
    HOURLY_THRESHOLD = float(os.getenv("HOURLY_THRESHOLD", "1.2"))  # 20% above historical max
    DAILY_THRESHOLD = float(os.getenv("DAILY_THRESHOLD", "1.2"))  # 20% above historical max
    MONITOR_INTERVAL = int(os.getenv("MONITOR_INTERVAL", "10"))  # seconds
    RETRAIN_INTERVAL = int(os.getenv("RETRAIN_INTERVAL", "3600"))  # seconds (1 hour)

    # Leak Detection Parameters
    FORGOTTEN_TAP_THRESHOLD = float(os.getenv("FORGOTTEN_TAP_THRESHOLD", "0.5"))  # Liters/min
    FORGOTTEN_TAP_DURATION = int(os.getenv("FORGOTTEN_TAP_DURATION", "30"))  # Minutes
    TAP_LEAKAGE_THRESHOLD = float(os.getenv("TAP_LEAKAGE_THRESHOLD", "0.05"))
    MODEL_FILE = os.getenv("MODEL_FILE", "isolation_forest_model.pkl")

    # Logging Configuration
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    VERBOSE_LOGGING = os.getenv("VERBOSE_LOGGING", "false").lower() == "true"

    # Security Settings
    CORS_ORIGINS = os.getenv("CORS_ORIGINS", "http://localhost:3000,http://127.0.0.1:3000").split(",")
    CORS_ALLOW_ALL = os.getenv("CORS_ALLOW_ALL", "true").lower() == "true"

    # Development Settings
    ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
    DEBUG = os.getenv("DEBUG", "true").lower() == "true"
    USE_MOCK_DATA = os.getenv("USE_MOCK_DATA", "false").lower() == "true"

    # Notification Settings
    EMAIL_NOTIFICATIONS = os.getenv("EMAIL_NOTIFICATIONS", "false").lower() == "true"
    SMTP_SERVER = os.getenv("SMTP_SERVER", "smtp.gmail.com")
    SMTP_PORT = int(os.getenv("SMTP_PORT", "587"))
    EMAIL_USERNAME = os.getenv("EMAIL_USERNAME", "")
    EMAIL_PASSWORD = os.getenv("EMAIL_PASSWORD", "")
    NOTIFICATION_EMAILS = os.getenv("NOTIFICATION_EMAILS", "").split(",") if os.getenv("NOTIFICATION_EMAILS") else []

    # Performance Settings
    BATCH_SIZE = int(os.getenv("BATCH_SIZE", "1000"))
    DB_MIN_POOL_SIZE = int(os.getenv("DB_MIN_POOL_SIZE", "1"))
    DB_MAX_POOL_SIZE = int(os.getenv("DB_MAX_POOL_SIZE", "10"))
    ENABLE_CACHING = os.getenv("ENABLE_CACHING", "true").lower() == "true"
    CACHE_TTL = int(os.getenv("CACHE_TTL", "300"))

    # Backup and Maintenance
    AUTO_BACKUP = os.getenv("AUTO_BACKUP", "false").lower() == "true"
    BACKUP_DIR = os.getenv("BACKUP_DIR", "./backups")
    DATA_RETENTION_DAYS = int(os.getenv("DATA_RETENTION_DAYS", "365"))

settings = Settings()