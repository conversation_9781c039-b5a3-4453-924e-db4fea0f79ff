# AQUESA Leak Detection System - Git Ignore File

# =============================================================================
# Environment and Configuration Files
# =============================================================================

# Environment variables (contains sensitive information)
.env

# Local configuration overrides
.env.local
.env.*.local

# =============================================================================
# Python
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Virtual environments
myenv/
venv/
env/
ENV/
env.bak/
venv.bak/

# =============================================================================
# Machine Learning Models and Data
# =============================================================================

# Trained models
*.pkl
*.joblib
*.h5
*.model

# Data files
*.csv
*.json
*.parquet
*.feather

# Backup files
backups/
*.backup

# =============================================================================
# Logs and Temporary Files
# =============================================================================

# Log files
*.log
logs/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# =============================================================================
# IDE and Editor Files
# =============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/

# Jupyter Notebook
.ipynb_checkpoints

# Spyder
.spyderproject
.spyproject

# =============================================================================
# Operating System Files
# =============================================================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?

# Linux
*~

# =============================================================================
# Database Files
# =============================================================================

# SQLite databases
*.db
*.sqlite
*.sqlite3

# =============================================================================
# Frontend/Web Files
# =============================================================================

# Node modules (if using Node.js tools)
node_modules/

# Build outputs
build/
dist/

# =============================================================================
# Documentation
# =============================================================================

# Generated documentation
docs/_build/

# =============================================================================
# Custom AQUESA Files
# =============================================================================

# Local test data
test_data/
sample_data/

# Configuration backups
config_backup/

# Performance monitoring files
*.prof
*.profile
