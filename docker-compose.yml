version: '3.8'

services:
  # AQUESA API Backend Service
  aquesa-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: aquesa-api
    ports:
      - "${API_PORT:-8000}:8000"
    environment:
      - MONGO_URI=${MONGO_URI}
      - DB_NAME=${DB_NAME:-aquesa_management}
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - WINDOW_SIZE=${WINDOW_SIZE:-10}
      - HOURLY_THRESHOLD=${HOURLY_THRESHOLD:-1.2}
      - DAILY_THRESHOLD=${DAILY_THRESHOLD:-1.2}
      - MONITOR_INTERVAL=${MONITOR_INTERVAL:-10}
      - RETRAIN_INTERVAL=${RETRAIN_INTERVAL:-3600}
      - FORGOTTEN_TAP_THRESHOLD=${FORGOTTEN_TAP_THRESHOLD:-0.5}
      - FORGOTTEN_TAP_DURATION=${FORGOTTEN_TAP_DURATION:-30}
      - TAP_LEAKAGE_THRESHOLD=${TAP_LEAKAGE_THRESHOLD:-0.05}
      - MODEL_FILE=${MODEL_FILE:-isolation_forest_model.pkl}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - VERBOSE_LOGGING=${VERBOSE_LOGGING:-false}
      - CORS_ALLOW_ALL=${CORS_ALLOW_ALL:-true}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - DEBUG=${DEBUG:-false}
      - EMAIL_NOTIFICATIONS=${EMAIL_NOTIFICATIONS:-false}
      - SMTP_SERVER=${SMTP_SERVER:-smtp.gmail.com}
      - SMTP_PORT=${SMTP_PORT:-587}
      - EMAIL_USERNAME=${EMAIL_USERNAME}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
      - NOTIFICATION_EMAILS=${NOTIFICATION_EMAILS}
      - BATCH_SIZE=${BATCH_SIZE:-1000}
      - ENABLE_CACHING=${ENABLE_CACHING:-true}
      - CACHE_TTL=${CACHE_TTL:-300}
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
      - ./backups:/app/backups
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - aquesa-network

  # AQUESA Frontend Service
  aquesa-frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: aquesa-frontend
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      - FRONTEND_PORT=3000
      - API_BASE_URL=http://aquesa-api:8000/api
    depends_on:
      - aquesa-api
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    networks:
      - aquesa-network

  # MongoDB Service (Optional - if you want to run MongoDB in Docker)
  mongodb:
    image: mongo:6.0
    container_name: aquesa-mongodb
    ports:
      - "${MONGO_PORT:-27017}:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME:-admin}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD:-password}
      - MONGO_INITDB_DATABASE=${DB_NAME:-aquesa_management}
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - aquesa-network
    profiles:
      - with-mongodb

  # Redis Service (Optional - for caching)
  redis:
    image: redis:7-alpine
    container_name: aquesa-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - aquesa-network
    profiles:
      - with-redis

  # Nginx Reverse Proxy (Optional - for production)
  nginx:
    image: nginx:alpine
    container_name: aquesa-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - aquesa-api
      - aquesa-frontend
    restart: unless-stopped
    networks:
      - aquesa-network
    profiles:
      - with-nginx

volumes:
  mongodb_data:
  redis_data:

networks:
  aquesa-network:
    driver: bridge
