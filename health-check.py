#!/usr/bin/env python3
"""
AQUESA Health Check Script for Docker Deployment
Checks the health of all AQUESA services.
"""

import requests
import sys
import time
import json
from datetime import datetime

# Service endpoints to check
SERVICES = {
    'API': 'http://localhost:8000',
    'Frontend': 'http://localhost:3000',
    'API Docs': 'http://localhost:8000/docs',
    'API Health': 'http://localhost:8000/',
}

def check_service(name, url, timeout=10):
    """Check if a service is healthy."""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            return True, f"✅ {name}: OK ({response.status_code})"
        else:
            return False, f"❌ {name}: HTTP {response.status_code}"
    except requests.exceptions.ConnectionError:
        return False, f"❌ {name}: Connection refused"
    except requests.exceptions.Timeout:
        return False, f"❌ {name}: Timeout"
    except Exception as e:
        return False, f"❌ {name}: {str(e)}"

def check_api_functionality():
    """Check specific API functionality."""
    try:
        # Test leak detection endpoint
        response = requests.get('http://localhost:8000/api/leaks', timeout=10)
        if response.status_code == 200:
            return True, "✅ API Functionality: Leak detection endpoint working"
        else:
            return False, f"❌ API Functionality: Leak endpoint returned {response.status_code}"
    except Exception as e:
        return False, f"❌ API Functionality: {str(e)}"

def main():
    """Main health check function."""
    print("🏥 AQUESA Health Check")
    print("=" * 50)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    all_healthy = True
    results = []
    
    # Check basic services
    for service_name, url in SERVICES.items():
        healthy, message = check_service(service_name, url)
        results.append((healthy, message))
        print(message)
        if not healthy:
            all_healthy = False
    
    print()
    
    # Check API functionality
    healthy, message = check_api_functionality()
    results.append((healthy, message))
    print(message)
    if not healthy:
        all_healthy = False
    
    print()
    print("=" * 50)
    
    if all_healthy:
        print("🎉 All services are healthy!")
        return 0
    else:
        print("⚠️  Some services are not healthy!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
