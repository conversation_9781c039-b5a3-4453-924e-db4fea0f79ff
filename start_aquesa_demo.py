#!/usr/bin/env python3
"""
Complete AQUESA Demo Startup Script
This starts both the API server and frontend for a complete demo.
"""

import subprocess
import sys
import time
import os
import webbrowser
from threading import Timer

def start_test_api():
    """Start the test API server."""
    print("🚀 Starting AQUESA Test API Server...")
    
    # Start the test API server
    api_process = subprocess.Popen([
        sys.executable, "test_api_server.py"
    ], cwd=os.getcwd())
    
    return api_process

def start_frontend():
    """Start the frontend server."""
    print("🌐 Starting Frontend Server...")
    
    frontend_dir = os.path.join(os.getcwd(), "frontend")
    
    # Start the frontend server
    frontend_process = subprocess.Popen([
        sys.executable, "-m", "http.server", "3000"
    ], cwd=frontend_dir)
    
    return frontend_process

def open_browser():
    """Open browser after servers are ready."""
    print("🌐 Opening browser...")
    webbrowser.open("http://localhost:3000")

def main():
    """Main function to start the complete demo."""
    print("🚀 AQUESA Smart Water Leak Detection System - Demo Startup")
    print("=" * 60)
    
    try:
        # Start API server
        api_process = start_test_api()
        print("✅ Test API server started (PID: {})".format(api_process.pid))
        
        # Wait a moment for API to start
        time.sleep(3)
        
        # Start frontend server
        frontend_process = start_frontend()
        print("✅ Frontend server started (PID: {})".format(frontend_process.pid))
        
        # Wait a moment for frontend to start
        time.sleep(2)
        
        print("\n🎉 AQUESA Demo is ready!")
        print("📡 API Server: http://localhost:8000")
        print("🌐 Frontend: http://localhost:3000")
        print("\n🛑 Press Ctrl+C to stop all servers")
        print("-" * 60)
        
        # Open browser after 3 seconds
        Timer(3.0, open_browser).start()
        
        # Wait for user to stop
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping servers...")
            
            # Stop processes
            api_process.terminate()
            frontend_process.terminate()
            
            # Wait for processes to stop
            api_process.wait()
            frontend_process.wait()
            
            print("✅ All servers stopped")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
