// AQUESA Leak Detection System Frontend Application

class AQUESADashboard {
    constructor() {
        // Get API base URL from environment or use default
        this.apiBaseUrl = window.location.hostname === 'localhost'
            ? 'http://localhost:8000/api'
            : `${window.location.protocol}//${window.location.hostname}:8000/api`;
        this.refreshInterval = 30000; // 30 seconds
        this.autoRefresh = true;
        this.soundAlerts = true;
        this.intervalId = null;
        this.charts = {};

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSettings();
        this.updateConnectionStatus('connecting');
        this.loadInitialData();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // Control buttons
        document.getElementById('start-monitoring').addEventListener('click', () => this.startMonitoring());
        document.getElementById('stop-monitoring').addEventListener('click', () => this.stopMonitoring());
        document.getElementById('refresh-btn').addEventListener('click', () => this.refreshAll());
        
        // Tab-specific refresh buttons
        document.getElementById('refresh-leaks').addEventListener('click', () => this.loadLeakData());
        document.getElementById('refresh-dwellings').addEventListener('click', () => this.loadDwellingData());
        
        // Search functionality
        document.getElementById('search-btn').addEventListener('click', () => this.searchDwellings());
        document.getElementById('dwelling-search').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.searchDwellings();
        });
        
        // Settings
        document.getElementById('save-settings').addEventListener('click', () => this.saveSettings());
        document.getElementById('reset-settings').addEventListener('click', () => this.resetSettings());
        document.getElementById('view-statistics').addEventListener('click', () => this.showStatistics());
        
        // Tab change events
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => this.onTabChange(e.target.getAttribute('data-bs-target')));
        });
    }

    async loadInitialData() {
        try {
            await Promise.all([
                this.loadLeakData(),
                this.loadDwellingData(),
                this.loadStatistics()
            ]);
            this.updateConnectionStatus('connected');
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.updateConnectionStatus('disconnected');
        }
    }

    async loadLeakData() {
        try {
            const response = await axios.get(`${this.apiBaseUrl}/leaks`);
            const leaks = response.data.leaks || [];
            
            this.updateLeakTable(leaks);
            this.updateLeakStatistics(leaks);
            
            // Play sound alert for new leaks
            if (this.soundAlerts && leaks.length > 0) {
                this.playAlertSound();
            }
            
        } catch (error) {
            console.error('Error loading leak data:', error);
            this.showError('Failed to load leak data');
        }
    }

    async loadDwellingData() {
        try {
            const response = await axios.get(`${this.apiBaseUrl}/dwelling-ids`);
            const data = response.data;
            
            this.updateDwellingTable(data.dwelling_ids || []);
            document.getElementById('total-dwellings').textContent = data.count || 0;
            
        } catch (error) {
            console.error('Error loading dwelling data:', error);
            this.showError('Failed to load dwelling data');
        }
    }

    async loadStatistics() {
        try {
            const response = await axios.get(`${this.apiBaseUrl}/dwelling-ids/statistics`);
            const stats = response.data;
            
            document.getElementById('clean-ids').textContent = stats.encoding_info?.clean_ids_count || 0;
            
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    updateLeakTable(leaks) {
        const tbody = document.getElementById('leaks-tbody');
        
        if (leaks.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted">
                        <i class="fas fa-check-circle me-2 text-success"></i>
                        No leaks detected - All systems normal
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = leaks.map(leak => `
            <tr class="fade-in">
                <td>${this.formatDateTime(leak.datatime)}</td>
                <td>
                    <span class="text-truncate-custom" title="${leak.dwellingid}">
                        ${leak.dwellingid}
                    </span>
                </td>
                <td>
                    <span class="badge bg-info">${leak['data.evt.csm']?.toFixed(2) || 'N/A'}</span>
                </td>
                <td>
                    <span class="leak-badge ${leak.type.toLowerCase()}">
                        ${this.formatLeakType(leak.type)}
                    </span>
                </td>
                <td class="text-truncate-custom" title="${leak.reason}">
                    ${leak.reason}
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="dashboard.viewDwellingDetails('${leak.dwellingid}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    updateDwellingTable(dwellingIds) {
        const tbody = document.getElementById('dwellings-tbody');
        
        if (dwellingIds.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center text-muted">
                        <i class="fas fa-home me-2"></i>No dwelling data available
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = dwellingIds.map((id, index) => `
            <tr class="slide-in">
                <td>${index + 1}</td>
                <td>
                    <span class="text-truncate-custom" title="${id}">
                        ${id}
                    </span>
                </td>
                <td>
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i> UTF-8 Clean
                    </span>
                </td>
                <td>
                    <span class="text-muted">
                        <i class="fas fa-clock me-1"></i>Recently active
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="dashboard.viewDwellingDetails('${id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="dashboard.viewDwellingData('${id}')">
                        <i class="fas fa-chart-line"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    updateLeakStatistics(leaks) {
        const totalLeaks = leaks.length;
        const floodLeaks = leaks.filter(leak => leak.type.toLowerCase().includes('flood')).length;
        
        document.getElementById('total-leaks').textContent = totalLeaks;
        document.getElementById('flood-leaks').textContent = floodLeaks;
    }

    async startMonitoring() {
        try {
            const response = await axios.post(`${this.apiBaseUrl}/monitoring/start`);
            if (response.data.status === 'Monitoring started') {
                document.getElementById('monitoring-status').textContent = 'Running';
                document.getElementById('monitoring-status').className = 'badge bg-success fs-6';
                this.showSuccess('Monitoring started successfully');
            }
        } catch (error) {
            console.error('Error starting monitoring:', error);
            this.showError('Failed to start monitoring: ' + (error.response?.data?.detail || error.message));
        }
    }

    async stopMonitoring() {
        try {
            const response = await axios.post(`${this.apiBaseUrl}/monitoring/stop`);
            if (response.data.status === 'Monitoring stopped') {
                document.getElementById('monitoring-status').textContent = 'Stopped';
                document.getElementById('monitoring-status').className = 'badge bg-secondary fs-6';
                this.showSuccess('Monitoring stopped successfully');
            }
        } catch (error) {
            console.error('Error stopping monitoring:', error);
            this.showError('Failed to stop monitoring: ' + (error.response?.data?.detail || error.message));
        }
    }

    async viewDwellingDetails(dwellingId) {
        try {
            const response = await axios.get(`${this.apiBaseUrl}/dwelling/${dwellingId}/data`);
            const data = response.data;
            
            const modalBody = document.getElementById('dwelling-modal-body');
            modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Dwelling Information</h6>
                        <p><strong>ID:</strong> ${data.dwelling_id}</p>
                        <p><strong>Encoding:</strong> ${data.encoding}</p>
                        <p><strong>Records:</strong> ${data.count}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Recent Activity</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Consumption</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.data.slice(0, 5).map(record => `
                                        <tr>
                                            <td>${this.formatDateTime(record.datatime)}</td>
                                            <td>${record['data.evt.csm']?.toFixed(2) || 'N/A'} L/min</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
            
            new bootstrap.Modal(document.getElementById('dwellingModal')).show();
            
        } catch (error) {
            console.error('Error loading dwelling details:', error);
            this.showError('Failed to load dwelling details');
        }
    }

    async showStatistics() {
        try {
            const response = await axios.get(`${this.apiBaseUrl}/dwelling-ids/statistics`);
            const stats = response.data;
            
            const modalBody = document.getElementById('statistics-modal-body');
            modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Encoding Statistics</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Total Dwellings:</span>
                                <strong>${stats.total_dwellings}</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Clean IDs:</span>
                                <strong class="text-success">${stats.encoding_info?.clean_ids_count || 0}</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Encoding:</span>
                                <strong>${stats.encoding_info?.encoding || 'UTF-8'}</strong>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>System Health</h6>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-success" style="width: ${(stats.encoding_info?.clean_ids_count / stats.total_dwellings * 100) || 100}%">
                                ${Math.round((stats.encoding_info?.clean_ids_count / stats.total_dwellings * 100) || 100)}%
                            </div>
                        </div>
                        <p class="text-muted">Encoding health status</p>
                    </div>
                </div>
            `;
            
            new bootstrap.Modal(document.getElementById('statisticsModal')).show();
            
        } catch (error) {
            console.error('Error loading statistics:', error);
            this.showError('Failed to load statistics');
        }
    }

    searchDwellings() {
        const searchTerm = document.getElementById('dwelling-search').value.toLowerCase();
        const rows = document.querySelectorAll('#dwellings-tbody tr');
        
        rows.forEach(row => {
            const dwellingId = row.cells[1]?.textContent.toLowerCase() || '';
            if (dwellingId.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    updateConnectionStatus(status) {
        const indicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('connection-status');
        
        indicator.className = `fas fa-circle ${status}`;
        
        switch (status) {
            case 'connected':
                statusText.textContent = 'Connected';
                break;
            case 'disconnected':
                statusText.textContent = 'Disconnected';
                break;
            case 'connecting':
                statusText.textContent = 'Connecting...';
                break;
        }
    }

    startAutoRefresh() {
        if (this.autoRefresh && !this.intervalId) {
            this.intervalId = setInterval(() => {
                this.refreshAll();
            }, this.refreshInterval);
        }
    }

    stopAutoRefresh() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    refreshAll() {
        this.loadInitialData();
    }

    onTabChange(target) {
        switch (target) {
            case '#analytics':
                this.initializeCharts();
                break;
        }
    }

    initializeCharts() {
        // Initialize charts when analytics tab is shown
        if (!this.charts.leakTrends) {
            this.createLeakTrendsChart();
        }
        if (!this.charts.leakTypes) {
            this.createLeakTypesChart();
        }
    }

    createLeakTrendsChart() {
        const ctx = document.getElementById('leakTrendsChart').getContext('2d');
        this.charts.leakTrends = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'Leak Events',
                    data: [2, 1, 3, 0, 2, 1, 0],
                    borderColor: 'rgb(220, 53, 69)',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    createLeakTypesChart() {
        const ctx = document.getElementById('leakTypesChart').getContext('2d');
        this.charts.leakTypes = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Normal', 'Tap Leakage', 'Flood'],
                datasets: [{
                    data: [85, 12, 3],
                    backgroundColor: [
                        'rgb(25, 135, 84)',
                        'rgb(255, 193, 7)',
                        'rgb(220, 53, 69)'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    // Utility methods
    formatDateTime(dateString) {
        return new Date(dateString).toLocaleString();
    }

    formatLeakType(type) {
        return type.charAt(0).toUpperCase() + type.slice(1);
    }

    playAlertSound() {
        // Simple beep sound for alerts
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.value = 800;
        oscillator.type = 'sine';
        
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.5);
    }

    showSuccess(message) {
        this.showAlert(message, 'success');
    }

    showError(message) {
        this.showAlert(message, 'danger');
    }

    showAlert(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const container = document.querySelector('.container-fluid');
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                bootstrap.Alert.getOrCreateInstance(alert).close();
            }
        }, 5000);
    }

    saveSettings() {
        this.apiBaseUrl = document.getElementById('api-url').value;
        this.refreshInterval = parseInt(document.getElementById('refresh-interval').value) * 1000;
        this.autoRefresh = document.getElementById('auto-refresh').checked;
        this.soundAlerts = document.getElementById('sound-alerts').checked;
        
        localStorage.setItem('aquesa-settings', JSON.stringify({
            apiBaseUrl: this.apiBaseUrl,
            refreshInterval: this.refreshInterval,
            autoRefresh: this.autoRefresh,
            soundAlerts: this.soundAlerts
        }));
        
        this.stopAutoRefresh();
        if (this.autoRefresh) {
            this.startAutoRefresh();
        }
        
        this.showSuccess('Settings saved successfully');
    }

    loadSettings() {
        const settings = localStorage.getItem('aquesa-settings');
        if (settings) {
            const parsed = JSON.parse(settings);
            this.apiBaseUrl = parsed.apiBaseUrl || this.apiBaseUrl;
            this.refreshInterval = parsed.refreshInterval || this.refreshInterval;
            this.autoRefresh = parsed.autoRefresh !== undefined ? parsed.autoRefresh : this.autoRefresh;
            this.soundAlerts = parsed.soundAlerts !== undefined ? parsed.soundAlerts : this.soundAlerts;
            
            document.getElementById('api-url').value = this.apiBaseUrl;
            document.getElementById('refresh-interval').value = this.refreshInterval / 1000;
            document.getElementById('auto-refresh').checked = this.autoRefresh;
            document.getElementById('sound-alerts').checked = this.soundAlerts;
        }
    }

    resetSettings() {
        this.apiBaseUrl = 'http://localhost:8000/api';
        this.refreshInterval = 30000;
        this.autoRefresh = true;
        this.soundAlerts = true;
        
        document.getElementById('api-url').value = this.apiBaseUrl;
        document.getElementById('refresh-interval').value = 30;
        document.getElementById('auto-refresh').checked = true;
        document.getElementById('sound-alerts').checked = true;
        
        localStorage.removeItem('aquesa-settings');
        this.showSuccess('Settings reset to defaults');
    }
}

// Initialize the dashboard when the page loads
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new AQUESADashboard();
});
