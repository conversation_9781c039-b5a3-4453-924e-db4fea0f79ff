@echo off
echo 🚀 AQUESA Quick Start
echo.

echo Starting Test API Server...
start "AQUESA API" cmd /k "cd /d %~dp0 && python test_api_server.py"

echo Waiting for API server to start...
timeout /t 3 /nobreak >nul

echo Starting Frontend Server...
start "AQUESA Frontend" cmd /k "cd /d %~dp0frontend && python -m http.server 3000"

echo Waiting for frontend server to start...
timeout /t 2 /nobreak >nul

echo.
echo 🎉 AQUESA is starting up!
echo 📡 API Server: http://localhost:8000
echo 🌐 Frontend: http://localhost:3000
echo.
echo Opening browser in 3 seconds...
timeout /t 3 /nobreak >nul

start http://localhost:3000

echo.
echo ✅ AQUESA Demo is ready!
echo Close the terminal windows to stop the servers.
pause
