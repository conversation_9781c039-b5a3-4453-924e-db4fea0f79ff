# AQUESA Docker Environment Configuration
# This file is used for Docker deployments

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB Connection URI - Update with your MongoDB Atlas or local MongoDB
MONGO_URI=mongodb+srv://vijaybhaskar:<EMAIL>/?retryWrites=true&w=majority

# For local MongoDB in Docker (uncomment if using with-mongodb profile)
# MONGO_URI=*************************************************************************

# Database Name
DB_NAME=aquesa_management

# MongoDB Docker settings (if using local MongoDB)
MONGO_PORT=27017
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=password

# =============================================================================
# API SERVER CONFIGURATION
# =============================================================================

# API Server Port (host port mapping)
API_PORT=8000

# =============================================================================
# FRONTEND SERVER CONFIGURATION
# =============================================================================

# Frontend Server Port (host port mapping)
FRONTEND_PORT=3000

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Time window for historical data analysis (days)
WINDOW_SIZE=10

# Threshold multipliers for leak detection
HOURLY_THRESHOLD=1.2
DAILY_THRESHOLD=1.2

# Monitoring intervals (seconds)
MONITOR_INTERVAL=10
RETRAIN_INTERVAL=3600

# =============================================================================
# LEAK DETECTION PARAMETERS
# =============================================================================

# Forgotten tap detection settings
FORGOTTEN_TAP_THRESHOLD=0.5
FORGOTTEN_TAP_DURATION=30

# Tap leakage detection threshold
TAP_LEAKAGE_THRESHOLD=0.05

# Model file path
MODEL_FILE=isolation_forest_model.pkl

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Enable/Disable detailed logging
VERBOSE_LOGGING=false

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Enable/Disable CORS for all origins (use only in development)
CORS_ALLOW_ALL=true

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================

# Environment mode (development, production, testing)
ENVIRONMENT=production

# Enable/Disable debug mode
DEBUG=false

# Use test/mock data instead of real database
USE_MOCK_DATA=false

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================

# Enable/Disable email notifications for leaks
EMAIL_NOTIFICATIONS=false

# Email configuration (if notifications enabled)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USERNAME=
EMAIL_PASSWORD=

# Recipients for leak notifications (comma-separated)
NOTIFICATION_EMAILS=

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Maximum number of records to process in one batch
BATCH_SIZE=1000

# Cache settings
ENABLE_CACHING=true
CACHE_TTL=300

# Redis settings (if using with-redis profile)
REDIS_PORT=6379
